<?php

namespace App\Http\Controllers;

use App\Campaign;
use App\EmailMessage;
use App\EmailTemplate;
use App\Schedule;
use App\ScheduledProspect;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CampaignDailyStatsController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Show the daily stats view for a campaign.
     *
     * @param Campaign $campaign
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function show(Campaign $campaign)
    {
        // Load necessary relationships
        $campaign->load(['emailAccounts', 'campaignStages.emailTemplates']);

        return view('campaign-daily-stats.show', [
            'campaign' => $campaign,
        ]);
    }

    /**
     * Get the daily stats data for a campaign.
     *
     * @param Request $request
     * @param Campaign $campaign
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Request $request, Campaign $campaign)
    {
        // get day of week (mon, tue, wed, etc.)
        $today = strtolower(Carbon::now($campaign->carbon_timezone)->format('D'));
        $todayStart = Carbon::now($campaign->carbon_timezone)->startOfDay()->timezone(config('app.timezone'));
        $todayEnd = Carbon::now($campaign->carbon_timezone)->endOfDay()->timezone(config('app.timezone'));

        // 1. New contacts contacted today (first-time contacts)
        $newContactsToday = $this->getNewContactsToday($campaign, $todayStart, $todayEnd);

        // 2. Emails sent per email account
        $emailsPerAccount = $this->getEmailsPerAccount($campaign, $todayStart, $todayEnd);

        // 3. Emails sent per campaign stage and template
        $emailsPerStageAndTemplate = $this->getEmailsPerStageAndTemplate($campaign, $todayStart, $todayEnd);

        // 4. Delivery window distribution (detailed timeline)
        $deliveryWindowDistribution = $this->getDeliveryWindowDistribution($campaign, $todayStart, $todayEnd);

        // 5. Schedule completion percentage per email account
        $scheduleCompletion = $this->getScheduleCompletion($campaign, $today);

        // 6. Get today's schedule time range from email templates
        $scheduleTimeRange = $this->getScheduleTimeRange($campaign, $today);

        return response()->json([
            'status' => 'success',
            'data' => [
                'new_contacts_today' => $newContactsToday,
                'emails_per_account' => $emailsPerAccount,
                'emails_per_stage_template' => $emailsPerStageAndTemplate,
                'delivery_distribution' => $deliveryWindowDistribution,
                'schedule_completion' => $scheduleCompletion,
                'schedule_time_range' => $scheduleTimeRange,
                'last_updated' => Carbon::now($campaign->carbon_timezone)->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Get new contacts contacted today (first-time contacts).
     */
    private function getNewContactsToday($campaign, $todayStart, $todayEnd)
    {
        return EmailMessage::where('campaign_id', $campaign->id)
            ->where('origin', 'self')
            ->whereNotNull('email_template_id')
            ->whereBetween('submitted_at', [$todayStart, $todayEnd])
            ->whereHas('emailTemplate', function ($query) {
                $query->where('campaign_stage_number', 1);
            })
            ->distinct('prospect_id')
            ->count('prospect_id');
    }

    /**
     * Get emails sent per email account today.
     */
    private function getEmailsPerAccount($campaign, $todayStart, $todayEnd)
    {
        $emailsPerAccount = EmailMessage::where('campaign_id', $campaign->id)
            ->where('origin', 'self')
            ->whereNotNull('email_template_id')
            ->whereBetween('submitted_at', [$todayStart, $todayEnd])
            ->select('email_account_id', DB::raw('COUNT(*) as count'))
            ->groupBy('email_account_id')
            ->get()
            ->keyBy('email_account_id');

        // Ensure all campaign email accounts are included (even with 0 emails)
        $result = [];
        foreach ($campaign->emailAccounts as $account) {
            $emailCount = $emailsPerAccount->has($account->id) ? $emailsPerAccount->get($account->id)->count : 0;
            $result[] = [
                'email_account_id' => $account->id,
                'email_address' => $account->email_address,
                'count' => $emailCount,
            ];
        }

        return $result;
    }

    /**
     * Get emails sent per campaign stage and template today.
     */
    private function getEmailsPerStageAndTemplate($campaign, $todayStart, $todayEnd)
    {
        $emailsPerTemplate = EmailMessage::where('campaign_id', $campaign->id)
            ->where('origin', 'self')
            ->whereNotNull('email_template_id')
            ->whereBetween('submitted_at', [$todayStart, $todayEnd])
            ->select('email_template_id', DB::raw('COUNT(*) as count'))
            ->groupBy('email_template_id')
            ->get()
            ->keyBy('email_template_id');

        $result = [];
        foreach ($campaign->campaignStages->sortBy('number') as $stage) {
            $stageData = [
                'stage_number' => $stage->number,
                'stage_name' => "Stage {$stage->number}",
                'templates' => [],
                'total' => 0,
            ];

            foreach ($stage->emailTemplates->sortBy('number') as $template) {
                $count = $emailsPerTemplate->has($template->id) ? $emailsPerTemplate->get($template->id)->count : 0;
                $stageData['templates'][] = [
                    'template_id' => $template->id,
                    'template_number' => $template->number,
                    'subject' => $template->subject,
                    'count' => $count,
                ];
                $stageData['total'] += $count;
            }

            $result[] = $stageData;
        }

        return $result;
    }

    /**
     * Get delivery window distribution throughout the day.
     */
    private function getDeliveryWindowDistribution($campaign, $todayStart, $todayEnd)
    {
        // Get messages and convert timezone in PHP instead of MySQL for better reliability
        $messages = EmailMessage::where('campaign_id', $campaign->id)
            ->where('origin', 'self')
            ->whereNotNull('email_template_id')
            ->whereBetween('submitted_at', [$todayStart, $todayEnd])
            ->select('email_account_id', 'submitted_at')
            ->get();

        $distribution = [];
        foreach ($campaign->emailAccounts as $account) {
            $accountMessages = $messages->where('email_account_id', $account->id);
            $timeSlots = [];

            // Group by hour and 15-minute intervals using Carbon for timezone conversion
            foreach ($accountMessages as $message) {
                // Convert UTC timestamp to campaign timezone
                $localTime = Carbon::parse($message->submitted_at)->timezone($campaign->carbon_timezone);
                $hour = $localTime->hour;
                $minute = intval(floor($localTime->minute / 15) * 15);

                $timeKey = sprintf('%02d:%02d', $hour, $minute);
                if (!isset($timeSlots[$timeKey])) {
                    $timeSlots[$timeKey] = 0;
                }
                $timeSlots[$timeKey]++;
            }

            $distribution[] = [
                'email_account_id' => $account->id,
                'email_address' => $account->email_address,
                'time_slots' => $timeSlots,
            ];
        }

        return $distribution;
    }

    /**
     * Get schedule completion percentage per email account.
     */
    private function getScheduleCompletion($campaign, $today)
    {
        $result = [];

        foreach ($campaign->emailAccounts as $account) {
            // Get today's schedules for this account that have sent at least 1 message (activated)
            $todaySchedules = Schedule::where('campaign_id', $campaign->id)
                ->where('email_account_id', $account->id)
                ->where('day', $today)
                ->where('amount_sent', '>', 0) // Only activated schedules
                ->get();



            if ($todaySchedules->isEmpty()) {
                $result[] = [
                    'email_account_id' => $account->id,
                    'email_address' => $account->email_address,
                    'sent' => 0,
                    'remaining' => 0,
                    'total_scheduled' => 0,
                    'max_possible' => 0,
                    'completion_percentage' => 0,
                ];
                continue;
            }

            $sent = $todaySchedules->sum('amount_sent');
            $maxPossible = $todaySchedules->sum('amount_to_send');

            // Get remaining scheduled prospects for these activated schedules
            $scheduleIds = $todaySchedules->pluck('id');
            $remaining = ScheduledProspect::whereIn('schedule_id', $scheduleIds)->count();

            $totalScheduled = $sent + $remaining;
            $completionPercentage = $totalScheduled > 0 ? round(($sent / $totalScheduled) * 100, 1) : 0;

            $result[] = [
                'email_account_id' => $account->id,
                'email_address' => $account->email_address,
                'sent' => $sent,
                'remaining' => $remaining,
                'total_scheduled' => $totalScheduled,
                'max_possible' => $maxPossible,
                'completion_percentage' => $completionPercentage,
            ];
        }

        return $result;
    }
}
