<?php

namespace App;

use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

class ScheduledProspect extends Model
{
    protected $guarded = ['id'];

    protected $fillable = [
        'prospect_id',
        'schedule_id',
        'email_template_id',
        'linkedin_message_template_id',
    ];

    public $timestamps = false;

    public function scopeOfEmailTemplate($query, $emailTemplateId)
    {
        if (!($emailTemplateId instanceof Collection)) {
            $emailTemplateId = collect($emailTemplateId);
        }
        return $query->whereIn('email_template_id', $emailTemplateId);
    }

    public function scopeOfLinkedinMessageTemplate($query, $linkedinMessageTemplateId)
    {
        if (!($linkedinMessageTemplateId instanceof Collection)) {
            $linkedinMessageTemplateId = collect($linkedinMessageTemplateId);
        }
        return $query->whereIn('linkedin_message_template_id', $linkedinMessageTemplateId);
    }

    public function scopeOfSchedule($query, $scheduleId)
    {
        if ($scheduleId) {
            return $query->where('schedule_id', $scheduleId);
        }

        return $query;
    }

    public function prospect()
    {
        return $this->belongsTo(Prospect::class);
    }

    public function schedule()
    {
        return $this->belongsTo(Schedule::class);
    }

    public function emailTemplate()
    {
        return $this->belongsTo(EmailTemplate::class);
    }
}
