
/*
 |--------------------------------------------------------------------------
 | Laravel Spark Bootstrap
 |--------------------------------------------------------------------------
 |
 | First, we will load all of the "core" dependencies for Spark which are
 | libraries such as Vue and jQuery. This also loads the Spark helpers
 | for things such as HTTP calls, forms, and form validation errors.
 |
 | Next, we'll create the root Vue application for Spark. This will start
 | the entire application and attach it to the DOM. Of course, you may
 | customize this script as you desire and load your own components.
 |
 */

require('./spark-bootstrap');

// window.lodash = require('lodash');
// require('./papaparse.min');

/**
 * Javascript sprintf function for string formatting.
 */
window.sprintf = require('sprintf-js').sprintf;
window.vsprintf = require('sprintf-js').vsprintf;

/**
 * Vue Form Wizard registration
 * Github: https://github.com/cristijora/vue-form-wizard
 * Docs: http://vuejs.creative-tim.com/vue-form-wizard/#/
 */
import VueFormWizard from 'vue-form-wizard';
import 'vue-form-wizard/dist/vue-form-wizard.min.css';
Vue.use(VueFormWizard);

/**
 * Vue-chartist package.
 * A wrapper for the chartist.js library.
 */
Vue.use(require('vue-chartist'));
require('./vue-chartist-plugins-tooltip');

/**
 * Vue2 Dropzone registration.
 * Github: https://github.com/rowanwins/vue-dropzone
 * Docs: https://rowanwins.github.io/vue-dropzone/docs/dist/index.html
 */
import vue2Dropzone from 'vue2-dropzone';
import 'vue2-dropzone/dist/vue2Dropzone.min.css';
Vue.component('vue-dropzone', vue2Dropzone);

/**
 * Vue2 Slideout Panel Component
 * Github: https://github.com/officert/vue-slideout-panel
 * Docs: https://officert.github.io/vue-slideout-panel/#/home
 */
import VueSlideoutPanel from 'vue2-slideout-panel';
Vue.use(VueSlideoutPanel);

/**
 * Vue2 Tooltip Component
 * Github: https://github.com/Akryum/v-tooltip
 */
import VTooltip from 'v-tooltip';
Vue.use(VTooltip)

/*
 * Vue-tel-input component
 * Github: https://github.com/iamstevendao/vue-tel-input
 * Vue2 docs: https://iamstevendao.com/vue-tel-input/guide/legacy.html#installation
 */
import Vue from 'vue';
import VueTelInput from 'vue-tel-input';
import 'vue-tel-input/dist/vue-tel-input.css';

Vue.use(VueTelInput, {'mode': 'international', 'inputOptions': {'showDialCode':true, 'styleClasses': 'wavo-tel-input'}, 'styleClasses':'wavo-tel-input-wrapper', 'validCharactersOnly': true});

/**
 * TinyMCE
 * https://www.tiny.cloud/docs/general-configuration-guide/advanced-install/#packagemanagerinstalloptions
 * https://www.tiny.cloud/docs/integrations/vue/#tinymcevuejstechnicalreference
 * Solution to skip tiny cloud and load locally through webpack:
 * https://github.com/tinymce/tinymce-vue/issues/30#issuecomment-497005781
 */
// Use the following to publish css assets. Will need to place them all in /public/js folder.
// require.context(
//     'file-loader?name=[path][name].[ext]&context=node_modules/tinymce!tinymce/skins',
//     true,
//     /.*/
// );
// require.context(
//     'file-loader?name=[path][name].[ext]&context=node_modules/tinymce!tinymce/icons',
//     true,
//     /.*/
// );
// Main js plugin
import 'tinymce/tinymce';
// Theme
import 'tinymce/themes/silver/theme';
// Skins
// import 'tinymce/skins/ui/wavo/skin.min.css';
// import 'tinymce/skins/ui/wavo/content.min.css';
// import 'tinymce/skins/ui/wavo/content.inline.min.css';
// import 'tinymce/skins/content/wavo/content.min.css';
// Plugins
import 'tinymce/plugins/paste';
import 'tinymce/plugins/link';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/code';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/image';
import 'tinymce/plugins/autoresize';
// import Editor from "@tinymce/tinymce-vue"; // We'll load this inside our component.


import TestComponent from './components/test/test-component.vue'
Vue.component('TestComponent', TestComponent);

import TestComponent2 from './components/test/test-component2.vue'
Vue.component('TestComponent2', TestComponent2);

import CheckoutPlf from "./components/billing/CheckoutPlf.vue";
Vue.component('CheckoutPlf', CheckoutPlf);

import CheckoutWavo3eStartup from "./components/billing/CheckoutWavo3eStartup.vue";
Vue.component('CheckoutWavo3eStartup', CheckoutWavo3eStartup);

import CheckoutWavo3eFree from "./components/billing/CheckoutWavo3eFree.vue";
Vue.component('CheckoutWavo3eFree', CheckoutWavo3eFree);

import PlatformHostManager from "./components/admin/PlatformHostManager.vue";
Vue.component('PlatformHostManager', PlatformHostManager);

import CampaignDailyStats from "./components/campaigns/CampaignDailyStats.vue";
Vue.component('CampaignDailyStats', CampaignDailyStats);

// import swal from 'sweetalert';
window.swal = require('sweetalert');

// billing card
window.jQuery = $;
require("card");


require('./components/bootstrap');

import VCalendar from 'v-calendar';
Vue.use(VCalendar);

/**
 * Template startup script
 */
let Site = window.Site;

$(document).ready(function() {
    Site.run();
});

var app = new Vue({
    mixins: [require('spark')],

    data: {
        agency: Spark.agency,
        stripeElementMounted: false,
    },

    /**
     * The component has been created by Vue.
     */
    created() {
        var self = this;

        Bus.$on('updateAgency', function () {
            self.getAgency();
        });
    },

    methods: {
        /*
        * Get the current user's agency.
        */
        getAgency() {
            axios.get('/agency/current')
                .then(response => {
                    this.user.agency = response.data;
                    if (this.agency.id == this.user.agency_id) {
                        // If current agency is same as user's then also update current agency.
                        this.agency = response.data;
                    }
                });
        },
    }
});
