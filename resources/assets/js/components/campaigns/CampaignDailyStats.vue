<template>
  <div>
    <div class="page-content container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <p class="text-muted" v-if="lastUpdated">
                Last updated: {{ formatTime(lastUpdated) }}
              </p>
            </div>
            <div>
              <button
                class="btn btn-primary"
                @click="loadStats"
                :disabled="loading"
              >
                <i class="fa fa-refresh" :class="{ 'fa-spin': loading }"></i>
                {{ loading ? 'Loading...' : 'Refresh Data' }}
              </button>
            </div>
        </div>
      </div>
    </div>
  </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card card-shadow">
          <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="mb-0">{{ stats.new_contacts_today }}</h3>
                <p class="text-muted mb-0">New Contacts Today</p>
                <small class="text-success">{{ progressPercentage }}% of daily limit</small>
              </div>
              <div class="text-primary">
                <i class="fa fa-user-plus fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card card-shadow">
          <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="mb-0">{{ totalEmailsToday }}</h3>
                <p class="text-muted mb-0">Total Emails Sent</p>
                <small class="text-info">Across all accounts</small>
              </div>
              <div class="text-success">
                <i class="fa fa-envelope fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card card-shadow">
          <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="mb-0">{{ stats.emails_per_account.length }}</h3>
                <p class="text-muted mb-0">Active Accounts</p>
                <small class="text-info">Sending today</small>
              </div>
              <div class="text-warning">
                <i class="fa fa-users fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 mb-3">
        <div class="card card-shadow">
          <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h3 class="mb-0">{{ maxEmailsPerDay }}</h3>
                <p class="text-muted mb-0">Daily Limit</p>
                <small class="text-muted">Max new contacts</small>
              </div>
              <div class="text-secondary">
                <i class="fa fa-limit fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Details Row -->
    <div class="row">
      <!-- Emails per Account -->
      <div class="col-lg-6 mb-4">
        <div class="card card-shadow">
          <div class="card-header">
            <h5 class="mb-0">Emails Sent per Account</h5>
          </div>
          <div class="card-body">
            <div v-if="stats.emails_per_account.length === 0" class="text-center text-muted py-4">
              No emails sent today
            </div>
            <div v-else>
              <div v-for="account in stats.emails_per_account" :key="account.email_account_id" class="mb-3">
              <div class="d-flex justify-content-between align-items-center mb-1">
                <span class="font-weight-medium">{{ account.email_address }}</span>
                <span class="badge badge-primary">{{ account.count }}</span>
              </div>
              <div class="progress" style="height: 8px;">
                <div
                  class="progress-bar bg-primary"
                :style="{ width: totalEmailsToday > 0 ? (account.count / totalEmailsToday * 100) + '%' : '0%' }"
                ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- Schedule Completion -->
    <div class="col-lg-6 mb-4">
      <div class="card card-shadow">
        <div class="card-header">
          <h5 class="mb-0">Schedule Completion</h5>
        </div>
        <div class="card-body">
          <div v-if="stats.schedule_completion.length === 0" class="text-center text-muted py-4">
            No active schedules today
          </div>
          <div v-else>
            <div v-for="completion in stats.schedule_completion" :key="completion.email_account_id" class="mb-3">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span class="font-weight-medium">{{ completion.email_address }}</span>
              <span>Sent: {{ completion.sent }}, Remaining: {{ completion.remaining }}, Max: {{ completion.max_possible }}</span>
              <span class="text-muted">{{ completion.sent }}/{{ completion.total_scheduled }} ({{ completion.completion_percentage }}%)</span>
            </div>
            <div class="progress" style="height: 8px;">
              <div
                class="progress-bar"
              :class="getProgressBarClass(completion.completion_percentage)"
              :style="{ width: completion.completion_percentage + '%' }"
              ></div>
          </div>
<!--          <small class="text-muted">-->
<!--            Sent: {{ completion.sent }}, Remaining: {{ completion.remaining }}, Max: {{ completion.max_possible }}-->
<!--          </small>-->
        </div>
      </div>
    </div>
    </div>
    </div>
    </div>

    <!-- Emails per Stage and Template -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card card-shadow">
          <div class="card-header">
            <h5 class="mb-0">Emails Sent per Campaign Stage & Template</h5>
          </div>
          <div class="card-body">
            <div v-if="stats.emails_per_stage_template.length === 0" class="text-center text-muted py-4">
              No emails sent today
            </div>
            <div v-else>
              <div v-for="stage in stats.emails_per_stage_template" :key="stage.stage_number" class="mb-4">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">{{ stage.stage_name }}</h6>
                <span class="badge badge-secondary">{{ stage.total }} emails</span>
              </div>
              <div class="row">
                <div v-for="template in stage.templates" :key="template.template_id" class="col-md-6 col-lg-4 mb-2">
                <div class="border rounded p-3">
                  <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="font-weight-medium">Template {{ template.template_number }}</span>
                    <span class="badge badge-primary">{{ template.count }}</span>
                  </div>
                  <small class="text-muted">{{ template.subject }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    </div>

    <!-- Delivery Window Distribution Chart -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card card-shadow">
          <div class="card-header">
            <h5 class="mb-0">Message Delivery Distribution Throughout the Day</h5>
            <small class="text-muted">Shows when emails were sent for each account (15-minute intervals)</small>
          </div>
          <div class="card-body">
            <div v-if="stats.delivery_distribution.length === 0" class="text-center text-muted py-4">
              No emails sent today
            </div>
            <div v-else>
              <div ref="distributionChart" class="ct-chart ct-major-seventh" style="height: 300px;"></div>
              <div class="mt-3">
                <h6>Legend:</h6>
                <div class="row">
                  <div v-for="(account, index) in stats.delivery_distribution" :key="account.email_account_id" class="col-md-6 col-lg-4 mb-2">
                  <span class="d-inline-block w-3 h-3 mr-2" :style="{ backgroundColor: getChartColor(index) }"></span>
                {{ account.email_address }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    </div>
  </div>
</template>

<script>
  export default {
  name: 'CampaignDailyStats',

    props: ['user', 'campaign', 'uri'],

    data() {
        return {
            loading: false,
            lastUpdated: null,
            stats: {
                new_contacts_today: 0,
                emails_per_account: [],
                emails_per_stage_template: [],
                delivery_distribution: [],
                schedule_completion: []
            },
            chartOptions: {
                distributionChart: {
                    axisX: {
                        labelInterpolationFnc: function(value, index) {
                            // Show every 4th label (every hour) to avoid overcrowding
                            return index % 4 === 0 ? value : null;
                        }
                    },
                    axisY: {
                        onlyInteger: true,
                        scaleMinSpace: 20,
                        labelInterpolationFnc: function(value) {
                            return value;
                        }
                    },
                    plugins: [
                        Vue.chartist.plugins.tooltip({
                            tooltipFnc: function(meta, value) {
                                return meta + ': ' + value + ' emails';
                            }
                        })
                    ],
                    height: 400,
                    showPoint: true,
                    showLine: true,
                    showArea: false,
                    fullWidth: true,
                    chartPadding: {
                        top: 20,
                        right: 40,
                        bottom: 30,
                        left: 40
                    },
                    lineSmooth: Vue.chartist.Interpolation.cardinal({
                        tension: 0.1
                    }),
                    // Auto-scale Y axis to fit data better
                    high: undefined,
                    low: 0
                }
            },
            chartColors: ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22']
        };
    },

    computed: {
        totalEmailsToday() {
            return this.stats.emails_per_account.reduce((sum, account) => sum + account.count, 0);
        },

        maxEmailsPerDay() {
            return this.campaign.max_emails_per_day || 30;
        },

        progressPercentage() {
            return this.maxEmailsPerDay > 0 ? Math.round((this.stats.new_contacts_today / this.maxEmailsPerDay) * 100) : 0;
        }
    },

    mounted() {
        this.loadStats();
    },

    methods: {
        loadStats() {
            this.loading = true;

            axios.get(`/campaigns/${this.campaign.hashid}/daily-stats/data`)
                .then(response => {
                    if (response.data.status === 'success') {
                        this.stats = response.data.data;
                        this.lastUpdated = response.data.data.last_updated;
                        this.$nextTick(() => {
                            this.updateCharts();
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading stats:', error);
                    this.$toasted.error('Failed to load daily stats');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        updateCharts() {
            this.updateDeliveryDistributionChart();
        },

        updateDeliveryDistributionChart() {
            if (this.stats.delivery_distribution.length === 0) {
                console.log('No delivery distribution data available');
                return;
            }

            console.log('Delivery distribution data:', this.stats.delivery_distribution);

            const chartData = {
                labels: [],
                series: []
            };

            // Generate time labels (every 15 minutes)
            for (let hour = 0; hour < 24; hour++) {
                for (let minute = 0; minute < 60; minute += 15) {
                    chartData.labels.push(`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
                }
            }

            // Add series for each email account
            this.stats.delivery_distribution.forEach((account, accountIndex) => {
                const data = new Array(chartData.labels.length).fill(0);

                Object.keys(account.time_slots).forEach(timeKey => {
                    const labelIndex = chartData.labels.indexOf(timeKey);
                    if (labelIndex !== -1) {
                        data[labelIndex] = account.time_slots[timeKey];
                    }
                });

                chartData.series.push({
                    name: account.email_address,
                    data: data
                });
            });

            console.log('Chart data prepared:', chartData);

            // Update chart if element exists
            const chartElement = this.$refs.distributionChart;
            if (chartElement) {
                // Clear any existing chart
                chartElement.innerHTML = '';

                // Check if we have any data to display
                const hasData = chartData.series.some(series => series.data.some(value => value > 0));
                if (!hasData) {
                    chartElement.innerHTML = '<div class="text-center text-muted py-4">No email delivery data for today</div>';
                    return;
                }

                // Create new chart with proper error handling
                try {
                    console.log('Creating chart with Vue.chartist.Line');
                    new Vue.chartist.Line(chartElement, chartData, this.chartOptions.distributionChart);
                    console.log('Chart created successfully');
                } catch (error) {
                    console.error('Error creating chart:', error);
                    console.log('Chart data:', chartData);
                    console.log('Chart options:', this.chartOptions.distributionChart);
                    chartElement.innerHTML = '<div class="text-center text-danger py-4">Error loading chart</div>';
                }
            } else {
                console.error('Chart element not found');
            }
        },

        formatTime(timeString) {
            return new Date(timeString).toLocaleTimeString();
        },

        getProgressBarClass(percentage) {
            if (percentage >= 80) return 'bg-success';
            if (percentage >= 50) return 'bg-warning';
            return 'bg-danger';
        },

        getChartColor(index) {
            return this.chartColors[index % this.chartColors.length];
        }
    },
  }
</script>

<style scoped>
.ct-chart {
  min-height: 300px;
}

.ct-chart .ct-line {
  stroke-width: 3px;
}

.ct-chart .ct-point {
  stroke-width: 8px;
  stroke-linecap: round;
}

.ct-chart .ct-grid {
  stroke: rgba(0, 0, 0, 0.1);
  stroke-width: 1px;
  stroke-dasharray: 2px;
}

.ct-chart .ct-label {
  font-size: 12px;
  color: #666;
}

/* Custom colors for different series */
.ct-chart .ct-series-a .ct-line,
.ct-chart .ct-series-a .ct-point {
  stroke: #3498db;
}

.ct-chart .ct-series-b .ct-line,
.ct-chart .ct-series-b .ct-point {
  stroke: #e74c3c;
}

.ct-chart .ct-series-c .ct-line,
.ct-chart .ct-series-c .ct-point {
  stroke: #2ecc71;
}

.ct-chart .ct-series-d .ct-line,
.ct-chart .ct-series-d .ct-point {
  stroke: #f39c12;
}

.ct-chart .ct-series-e .ct-line,
.ct-chart .ct-series-e .ct-point {
  stroke: #9b59b6;
}

.ct-chart .ct-series-f .ct-line,
.ct-chart .ct-series-f .ct-point {
  stroke: #1abc9c;
}

.ct-chart .ct-series-g .ct-line,
.ct-chart .ct-series-g .ct-point {
  stroke: #34495e;
}

.ct-chart .ct-series-h .ct-line,
.ct-chart .ct-series-h .ct-point {
  stroke: #e67e22;
}
</style>
