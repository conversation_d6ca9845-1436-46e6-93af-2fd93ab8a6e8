@extends('layouts.app')

@section('page-title', 'Campaign Statistics: '.ucwords($campaign->name))

@section('page-styles')
  <link rel="stylesheet" href="/css/plugins.css">
  <link rel="stylesheet" href="/css/pages/stats-campaign.css">

  <style>
    .display-more-threads.btn-outline-primary:disabled {
      background-color: transparent !important;
      color: {{ Environment::getPrimaryColorCode() }} !important;
    }
    .trumbowyg-modal.trumbowyg-fixed-top {
      margin-top: -100px !important
    }
    #positives-by-day-bar-chart .ct-square:before {
      padding-bottom: 150px !important;
    }
    .rate-modal-btn {
      font-size: 14px;
      font-weight: normal;
    }
    .linkedin-thumbnails {
      width: 24px;
      height: 24px;
      margin-right: 2px;
    }
    .linkedin-thumbnails-initials {
      background: #E8E5DE;
      line-height: 25px;
      text-align: center;
      font-size: 11px;
      display: inline-block;
      text-transform: uppercase;
    }
    .thread .linkedin-thumbnails {
      position: absolute;
      top: 20px;
      left: 14px;
      width: 32px;
      height: 32px;
      padding:0;
    }
    .thread .linkedin-thumbnails-initials {
      position: absolute;
      top: 20px;
      left: 14px;
      width: 32px;
      height: 32px;
      padding:0;
      line-height: 32px;
      font-size: 14px;
    }
    .stage-statwrap {
      display: flex;
      flex-flow: row wrap;
      justify-content: space-between;
    }
    .stage-statitem {

    }
    .tag {
      font-size: 85%;
    }

    .card-new {
      border: 1px solid #e0e0e0 !important;
      background-color: #fff;
    }

    .thread-new {
      color: #37474f;
    }
  </style>
@endsection

@section('page-scripts')
  {{--<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.8.4/moment.min.js"></script>--}}
  <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js" ></script>
  <script src="//rawcdn.githack.com/RickStrahl/jquery-resizable/master/dist/jquery-resizable.min.js"></script>
  <script src="/js/datetime-moment.js"></script>
@endsection

@section('content-layout')
  <div class="page">
    <div class="page-header page-header-bordered">
      <h1 class="page-title">
        Campaign Daily Stats (live)
      </h1>
    </div>
    <campaign-daily-stats
        :user="user"
        :campaign="{{ $campaign }}"
        uri="{{ Request::path() }}"
    ></campaign-daily-stats>
  </div>
@endsection
